# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# Google Cloud / Vertex AI credentials
vertex-ai-key.json
ai-website-*.json
*-service-account.json
google-cloud-key.json

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# prisma
/prisma/migrations/
/prisma/dev.db
/prisma/dev.db-journal

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db
