// Simple test script to verify page generation
const { generateWebsite } = require('./lib/vertex-ai.ts');

async function testPageGeneration() {
  console.log('Testing page generation...\n');
  
  const testCases = [
    'create a login page',
    'build a dashboard for admin',
    'make a contact us page',
    'design an about page',
    'create a portfolio website',
    'build a blog page',
    'make an ecommerce store',
    'create a pricing page',
    'build a landing page for a restaurant'
  ];
  
  for (const prompt of testCases) {
    console.log(`Testing: "${prompt}"`);
    try {
      const result = await generateWebsite(prompt);
      const pageType = result.includes('Sign In') ? 'Login' :
                      result.includes('Dashboard') ? 'Dashboard' :
                      result.includes('Contact') ? 'Contact' :
                      result.includes('About') ? 'About' :
                      result.includes('Portfolio') ? 'Portfolio' :
                      result.includes('Blog') ? 'Blog' :
                      result.includes('ShopGenie') ? 'E-commerce' :
                      result.includes('Choose Your Plan') ? 'Pricing' :
                      'Landing';
      
      console.log(`✅ Generated: ${pageType} page`);
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
    console.log('');
  }
}

testPageGeneration();
